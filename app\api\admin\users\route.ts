import { NextRequest, NextResponse } from "next/server";
import User from "../../../../models/User";
import dbConnect from "../../../../lib/mongo";
import { requireAdmin, ADMIN_PERMISSIONS } from "../../../../lib/auth";

// GET - Lister tous les utilisateurs (Admin seulement)
export async function GET(req: NextRequest) {
  try {
    // Vérifier que l'utilisateur est admin
    const admin = await requireAdmin(req);
    console.log(`Admin ${admin.email} requesting users list`);

    await dbConnect();
    
    const users = await User.find({})
      .select("-password") // Exclure les mots de passe
      .sort({ createdAt: -1 });

    return NextResponse.json({
      users: users.map(user => ({
        _id: user._id,
        email: user.email,
        firstName: user.firstName,
        lastName: user.lastName,
        role: user.role,
        permissions: user.permissions,
        createdAt: user.createdAt,
        // Statistiques rapides
        totalQuizzes: user.quizResults?.length || 0,
        totalProjects: user.userProjects?.length || 0,
      }))
    });
  } catch (error: any) {
    console.error("Erreur GET /api/admin/users:", error);
    
    if (error.message === "Authentication required") {
      return NextResponse.json(
        { message: "Authentication required" },
        { status: 401 }
      );
    }
    
    if (error.message === "Admin access required") {
      return NextResponse.json(
        { message: "Admin access required" },
        { status: 403 }
      );
    }
    
    return NextResponse.json(
      { message: "Server error", error: error.message },
      { status: 500 }
    );
  }
}

// POST - Promouvoir un utilisateur en admin
export async function POST(req: NextRequest) {
  try {
    const admin = await requireAdmin(req);
    const { email, action } = await req.json();

    if (!email || !action) {
      return NextResponse.json(
        { message: "Email and action required" },
        { status: 400 }
      );
    }

    await dbConnect();
    const user = await User.findOne({ email });

    if (!user) {
      return NextResponse.json(
        { message: "User not found" },
        { status: 404 }
      );
    }

    let updateData: any = {};

    switch (action) {
      case "promote_to_admin":
        updateData = {
          role: "admin",
          permissions: ADMIN_PERMISSIONS
        };
        console.log(`Admin ${admin.email} promoting ${email} to admin`);
        break;

      case "demote_to_user":
        updateData = {
          role: "user",
          permissions: []
        };
        console.log(`Admin ${admin.email} demoting ${email} to user`);
        break;

      default:
        return NextResponse.json(
          { message: "Invalid action" },
          { status: 400 }
        );
    }

    await User.updateOne({ email }, updateData);

    return NextResponse.json({
      message: `User ${action.replace('_', ' ')} successfully`,
      user: {
        email: user.email,
        role: updateData.role,
        permissions: updateData.permissions
      }
    });

  } catch (error: any) {
    console.error("Erreur POST /api/admin/users:", error);
    
    if (error.message === "Authentication required") {
      return NextResponse.json(
        { message: "Authentication required" },
        { status: 401 }
      );
    }
    
    if (error.message === "Admin access required") {
      return NextResponse.json(
        { message: "Admin access required" },
        { status: 403 }
      );
    }
    
    return NextResponse.json(
      { message: "Server error", error: error.message },
      { status: 500 }
    );
  }
}
