import { NextRequest, NextResponse } from "next/server";
import User from "../../../models/User";
import Project from "../../../models/Project";
import dbConnect from "../../../lib/mongo";

// GET - Récupérer les projets personnels d'un utilisateur
export async function GET(req: NextRequest) {
  try {
    const { searchParams } = new URL(req.url);
    const email = searchParams.get("email");

    if (!email) {
      return NextResponse.json({ error: "Email manquant" }, { status: 400 });
    }

    await dbConnect();
    const user = await User.findOne({ email }).populate("userProjects.project");

    if (!user) {
      return NextResponse.json({ error: "Utilisateur non trouvé" }, { status: 404 });
    }

    return NextResponse.json({
      userProjects: user.userProjects.map((up: any) => ({
        _id: up._id,
        project: up.project,
        status: up.status,
        startedAt: up.startedAt,
        completedAt: up.completedAt,
        progress: up.progress,
        timeSpent: up.timeSpent,
        notes: up.notes,
        lastActivity: up.lastActivity
      }))
    });
  } catch (error) {
    console.error("Erreur GET /api/user-projects:", error);
    return NextResponse.json({ error: "Erreur serveur" }, { status: 500 });
  }
}

// POST - Ajouter un projet à un utilisateur (démarrer un projet)
export async function POST(req: NextRequest) {
  try {
    const { email, projectId } = await req.json();

    if (!email || !projectId) {
      return NextResponse.json({ error: "Email et projectId requis" }, { status: 400 });
    }

    await dbConnect();

    // Vérifier que le projet existe
    const project = await Project.findById(projectId);
    if (!project) {
      return NextResponse.json({ error: "Projet non trouvé" }, { status: 404 });
    }

    // Trouver l'utilisateur
    const user = await User.findOne({ email });
    if (!user) {
      return NextResponse.json({ error: "Utilisateur non trouvé" }, { status: 404 });
    }

    // Vérifier si l'utilisateur a déjà ce projet
    const existingProject = user.userProjects.find(
      (up: any) => up.project.toString() === projectId
    );

    if (existingProject) {
      return NextResponse.json({ 
        error: "Projet déjà ajouté à votre liste" 
      }, { status: 409 });
    }

    // Ajouter le projet à l'utilisateur
    user.userProjects.push({
      project: projectId,
      status: "in_progress",
      startedAt: new Date(),
      progress: 0,
      timeSpent: 0,
      notes: "",
      lastActivity: new Date()
    });

    await user.save();

    return NextResponse.json({ 
      message: "Projet ajouté avec succès",
      userProject: user.userProjects[user.userProjects.length - 1]
    });
  } catch (error) {
    console.error("Erreur POST /api/user-projects:", error);
    return NextResponse.json({ error: "Erreur serveur" }, { status: 500 });
  }
}

// PUT - Mettre à jour la progression d'un projet
export async function PUT(req: NextRequest) {
  try {
    const { email, projectId, status, progress, timeSpent, notes } = await req.json();

    if (!email || !projectId) {
      return NextResponse.json({ error: "Email et projectId requis" }, { status: 400 });
    }

    await dbConnect();
    const user = await User.findOne({ email });

    if (!user) {
      return NextResponse.json({ error: "Utilisateur non trouvé" }, { status: 404 });
    }

    // Trouver le projet utilisateur
    const userProjectIndex = user.userProjects.findIndex(
      (up: any) => up.project.toString() === projectId
    );

    if (userProjectIndex === -1) {
      return NextResponse.json({ error: "Projet non trouvé dans votre liste" }, { status: 404 });
    }

    // Mettre à jour les champs fournis
    if (status !== undefined) user.userProjects[userProjectIndex].status = status;
    if (progress !== undefined) user.userProjects[userProjectIndex].progress = progress;
    if (timeSpent !== undefined) user.userProjects[userProjectIndex].timeSpent = timeSpent;
    if (notes !== undefined) user.userProjects[userProjectIndex].notes = notes;
    
    // Mettre à jour la date de dernière activité
    user.userProjects[userProjectIndex].lastActivity = new Date();
    
    // Si le projet est complété, ajouter la date de fin
    if (status === "completed") {
      user.userProjects[userProjectIndex].completedAt = new Date();
      user.userProjects[userProjectIndex].progress = 100;
    }

    await user.save();

    return NextResponse.json({ 
      message: "Progression mise à jour",
      userProject: user.userProjects[userProjectIndex]
    });
  } catch (error) {
    console.error("Erreur PUT /api/user-projects:", error);
    return NextResponse.json({ error: "Erreur serveur" }, { status: 500 });
  }
}
