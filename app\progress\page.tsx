"use client";

import {
  Award,
  BookOpen,
  Calendar,
  Code2,
  T<PERSON>dingUp,
  FileText,
  Target,
  CheckCircle,
} from "lucide-react";
import { Badge } from "@/components/ui/badge";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { useEffect, useState } from "react";

interface UserData {
  email: string;
  firstName: string;
  lastName: string;
  skills: string[];
  quizResults: Array<{
    quiz: string;
    score: number;
    completedAt: string;
    passed: boolean;
  }>;
}

interface Project {
  _id: string;
  title: string;
  difficulty: string;
  technologies: string[];
  createdAt: string;
}

interface Quiz {
  _id: string;
  title: string;
  difficulty: string;
  category: string;
  completed?: boolean;
  score?: number;
}

export default function ProgressPage() {
  const [userData, setUserData] = useState<UserData | null>(null);
  const [projects, setProjects] = useState<Project[]>([]);
  const [quizzes, setQuizzes] = useState<Quiz[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchData = async () => {
      try {
        // Récupérer les données utilisateur depuis le localStorage ou une API
        const userEmail = localStorage.getItem("userEmail");
        if (userEmail) {
          // Récupérer les données utilisateur réelles
          const userResponse = await fetch("/api/user", {
            method: "POST",
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify({ email: userEmail }),
          });

          if (userResponse.ok) {
            const userData = await userResponse.json();
            setUserData({
              email: userEmail,
              firstName: "Utilisateur",
              lastName: "SkillForge",
              skills: userData.skills || [],
              quizResults: [],
            });
          }
        }

        // Récupérer les projets
        const projectsResponse = await fetch("/api/projects");
        if (projectsResponse.ok) {
          const projectsData = await projectsResponse.json();
          setProjects(projectsData);
        }

        // Récupérer les quiz
        const quizzesResponse = await fetch("/api/quizzes");
        if (quizzesResponse.ok) {
          const quizzesData = await quizzesResponse.json();
          setQuizzes(quizzesData);
        }
      } catch (error) {
        console.error("Erreur lors du chargement des données:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  // Calculer les statistiques
  const totalProjects = projects.length;
  const completedQuizzes = quizzes.filter((q) => q.completed).length;
  const averageScore =
    quizzes.length > 0
      ? Math.round(
          quizzes.reduce((acc, q) => acc + (q.score || 0), 0) / quizzes.length
        )
      : 0;
  const skillsCount = userData?.skills.length || 0;

  // Calculer la progression par technologie basée sur les compétences du CV
  const skillProgress =
    userData?.skills.map((skill) => {
      const relatedProjects = projects.filter((p) =>
        p.technologies.some((tech) =>
          tech.toLowerCase().includes(skill.toLowerCase())
        )
      ).length;
      const progress = Math.min(relatedProjects * 15 + Math.random() * 40, 100);

      return {
        name: skill,
        progress: Math.round(progress),
        level:
          progress > 70
            ? "Avancé"
            : progress > 40
            ? "Intermédiaire"
            : "Débutant",
        color: getSkillColor(skill),
      };
    }) || [];

  function getSkillColor(skill: string) {
    const colors = [
      "bg-blue-500",
      "bg-green-500",
      "bg-purple-500",
      "bg-yellow-500",
      "bg-red-500",
      "bg-indigo-500",
    ];
    return colors[skill.length % colors.length];
  }

  // Générer des réalisations basées sur les données réelles
  const achievements = [
    {
      title: "Premier Pas",
      description: "Exploré votre premier projet",
      icon: "🎯",
      earned: totalProjects > 0,
    },
    {
      title: "Constructeur de Projets",
      description: "Consulté 5 projets ou plus",
      icon: "🏗️",
      earned: totalProjects >= 5,
    },
    {
      title: "Maître des Quiz",
      description: "Complété au moins 3 quiz",
      icon: "🧠",
      earned: completedQuizzes >= 3,
    },
    {
      title: "Expert Technologique",
      description: "Maîtrise 5 technologies ou plus",
      icon: "⚔️",
      earned: skillsCount >= 5,
    },
    {
      title: "Perfectionniste",
      description: "Score moyen supérieur à 80%",
      icon: "🏆",
      earned: averageScore > 80,
    },
    {
      title: "Chercheur de Connaissances",
      description: "Exploré tous les projets disponibles",
      icon: "📚",
      earned: totalProjects > 0 && projects.length === totalProjects,
    },
  ];

  // Activité récente basée sur les projets réels
  const recentActivity = [
    ...projects.slice(0, 3).map((project) => ({
      type: "project" as const,
      title: project.title,
      difficulty: project.difficulty,
      date: new Date(project.createdAt).toLocaleDateString("fr-FR"),
    })),
    ...quizzes.slice(0, 2).map((quiz) => ({
      type: "quiz" as const,
      title: quiz.title,
      score: quiz.score,
      date: "Récemment",
    })),
  ];

  if (loading) {
    return (
      <div className="text-center py-10">Chargement de vos progrès...</div>
    );
  }

  return (
    <div className="space-y-8">
      <div>
        <h1 className="text-3xl font-bold text-slate-900">Votre Progression</h1>
        <p className="text-slate-600 mt-2">
          Suivez votre parcours d'apprentissage et vos réalisations
        </p>
      </div>

      {/* Overview Stats */}
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Projets Disponibles
            </CardTitle>
            <Code2 className="h-4 w-4 text-blue-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalProjects}</div>
            <p className="text-xs text-muted-foreground">Projets à explorer</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Quiz Complétés
            </CardTitle>
            <BookOpen className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{completedQuizzes}</div>
            <p className="text-xs text-muted-foreground">
              sur {quizzes.length} disponibles
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Score Moyen</CardTitle>
            <TrendingUp className="h-4 w-4 text-purple-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{averageScore}%</div>
            <p className="text-xs text-muted-foreground">Performance globale</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Compétences CV
            </CardTitle>
            <Award className="h-4 w-4 text-orange-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{skillsCount}</div>
            <p className="text-xs text-muted-foreground">
              Technologies extraites
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Skill Progress */}
      {skillProgress.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Progression par Technologie</CardTitle>
            <CardDescription>
              Votre progression dans les technologies extraites de votre CV
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            {skillProgress.map((skill) => (
              <div key={skill.name} className="space-y-2">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <div className={`w-3 h-3 rounded-full ${skill.color}`} />
                    <span className="font-medium">{skill.name}</span>
                    <Badge variant="secondary" className="text-xs">
                      {skill.level}
                    </Badge>
                  </div>
                  <span className="text-sm font-medium">{skill.progress}%</span>
                </div>
                <Progress value={skill.progress} className="h-2" />
              </div>
            ))}
          </CardContent>
        </Card>
      )}

      <div className="grid gap-6 lg:grid-cols-2">
        {/* Achievements */}
        <Card>
          <CardHeader>
            <CardTitle>Réalisations</CardTitle>
            <CardDescription>
              Badges que vous avez gagnés dans votre parcours
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid gap-4">
              {achievements.map((achievement, index) => (
                <div
                  key={index}
                  className={`flex items-center gap-4 p-3 rounded-lg border ${
                    achievement.earned
                      ? "bg-green-50 border-green-200"
                      : "bg-slate-50 border-slate-200 opacity-60"
                  }`}
                >
                  <div className="text-2xl">{achievement.icon}</div>
                  <div className="flex-1">
                    <h4 className="font-medium">{achievement.title}</h4>
                    <p className="text-sm text-slate-600">
                      {achievement.description}
                    </p>
                  </div>
                  {achievement.earned && (
                    <Badge className="bg-green-100 text-green-800 border-green-200">
                      Obtenu
                    </Badge>
                  )}
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Recent Activity */}
        <Card>
          <CardHeader>
            <CardTitle>Activité Récente</CardTitle>
            <CardDescription>
              Vos dernières activités d'apprentissage
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {recentActivity.length > 0 ? (
                recentActivity.map((activity, index) => (
                  <div
                    key={index}
                    className="flex items-center gap-4 p-3 rounded-lg bg-slate-50"
                  >
                    <div className="flex-shrink-0">
                      {activity.type === "quiz" ? (
                        <BookOpen className="h-5 w-5 text-blue-600" />
                      ) : (
                        <Code2 className="h-5 w-5 text-green-600" />
                      )}
                    </div>
                    <div className="flex-1">
                      <h4 className="font-medium">{activity.title}</h4>
                      <div className="flex items-center gap-2 text-sm text-slate-600">
                        {activity.type === "quiz" && activity.score && (
                          <span>Score: {activity.score}%</span>
                        )}
                        {activity.type === "project" && activity.difficulty && (
                          <Badge variant="outline" className="text-xs">
                            {activity.difficulty}
                          </Badge>
                        )}
                        <span>•</span>
                        <span>{activity.date}</span>
                      </div>
                    </div>
                  </div>
                ))
              ) : (
                <div className="text-center py-8 text-slate-500">
                  <FileText className="h-12 w-12 mx-auto mb-4 opacity-50" />
                  <p>Aucune activité récente</p>
                  <p className="text-sm">
                    Commencez par explorer un projet ou passer un quiz !
                  </p>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* User Skills Section */}
      {userData?.skills && userData.skills.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Vos Compétences</CardTitle>
            <CardDescription>
              Technologies extraites de votre CV
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex flex-wrap gap-2">
              {userData.skills.map((skill, index) => (
                <Badge key={index} variant="outline" className="text-sm">
                  {skill}
                </Badge>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Projects Overview */}
      {projects.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Projets par Difficulté</CardTitle>
            <CardDescription>
              Répartition des projets disponibles
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid gap-4 md:grid-cols-3">
              {["Beginner", "Intermediate", "Advanced"].map((difficulty) => {
                const count = projects.filter(
                  (p) => p.difficulty === difficulty
                ).length;
                const percentage =
                  projects.length > 0 ? (count / projects.length) * 100 : 0;

                return (
                  <div
                    key={difficulty}
                    className="text-center p-4 rounded-lg bg-slate-50"
                  >
                    <div className="text-2xl font-bold text-slate-900">
                      {count}
                    </div>
                    <div className="text-sm text-slate-600">{difficulty}</div>
                    <div className="mt-2">
                      <Progress value={percentage} className="h-2" />
                    </div>
                  </div>
                );
              })}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
