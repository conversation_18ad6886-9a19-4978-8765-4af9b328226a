"use client";

import {
  <PERSON><PERSON><PERSON>,
  Code2,
  FileText,
  Target,
  CheckCircle,
  Clock,
} from "lucide-react";
import { Badge } from "@/components/ui/badge";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { useEffect, useState } from "react";

interface UserProgressData {
  user: {
    email: string;
    firstName: string;
    lastName: string;
    skills: string[];
    cvUrl?: string;
  };
  statistics: {
    completedQuizzes: number;
    passedQuizzes: number;
    averageScore: number;
    totalXP: number;
  };
  quizResults: Array<{
    quizId: string;
    quizTitle: string;
    score: number;
    passed: boolean;
    completedAt: string;
    difficulty: string;
  }>;
}

interface Project {
  _id: string;
  title: string;
  difficulty: string;
  technologies: string[];
  createdAt: string;
}

interface Quiz {
  _id: string;
  title: string;
  difficulty: string;
  category: string;
}

export default function ProgressPage() {
  const [userProgress, setUserProgress] = useState<UserProgressData | null>(
    null
  );
  const [projects, setProjects] = useState<Project[]>([]);
  const [allQuizzes, setAllQuizzes] = useState<Quiz[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchData = async () => {
      try {
        // Récupérer l'email depuis localStorage
        let userEmail = localStorage.getItem("email");

        if (!userEmail) {
          const userObj = localStorage.getItem("user");
          if (userObj) {
            try {
              const parsedUser = JSON.parse(userObj);
              userEmail = parsedUser.email;
            } catch (e) {
              console.error("Erreur parsing user object:", e);
            }
          }
        }

        if (!userEmail) {
          setError("Aucun utilisateur connecté. Veuillez vous reconnecter.");
          setLoading(false);
          return;
        }

        // Récupérer les données utilisateur
        const userResponse = await fetch("/api/user", {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({ email: userEmail }),
        });

        if (userResponse.ok) {
          const userData = await userResponse.json();
          setUserProgress(userData);
        } else {
          setError("Erreur lors du chargement des données utilisateur");
        }

        // Récupérer les projets et quiz
        const [projectsRes, quizzesRes] = await Promise.all([
          fetch("/api/projects"),
          fetch("/api/quiz"),
        ]);

        if (projectsRes.ok) {
          const projectsData = await projectsRes.json();
          setProjects(projectsData);
        }

        if (quizzesRes.ok) {
          const quizzesData = await quizzesRes.json();
          setAllQuizzes(quizzesData);
        }
      } catch (error) {
        console.error("Erreur lors du chargement des données:", error);
        setError("Erreur de connexion");
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-slate-600">Chargement de vos données...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <FileText className="h-16 w-16 mx-auto mb-4 text-red-400" />
          <h2 className="text-xl font-semibold text-slate-900 mb-2">Erreur</h2>
          <p className="text-slate-600 mb-4">{error}</p>
          <button
            onClick={() => window.location.reload()}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            Réessayer
          </button>
        </div>
      </div>
    );
  }

  const stats = userProgress?.statistics || {
    completedQuizzes: 0,
    passedQuizzes: 0,
    averageScore: 0,
    totalXP: 0,
  };

  const userData = userProgress?.user;
  const totalQuizzes = allQuizzes.length;
  const recentQuizzes = userProgress?.quizResults.slice(0, 5) || [];

  return (
    <div className="space-y-6">
      {/* En-tête avec informations utilisateur */}
      <div className="bg-gradient-to-r from-blue-600 to-purple-600 rounded-xl p-6 text-white">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold">
              Bonjour {userData?.firstName || "Utilisateur"} !
            </h1>
            <p className="text-blue-100 mt-1">
              Voici un aperçu de votre progression
            </p>
          </div>
          <div className="text-right">
            <div className="text-3xl font-bold">{stats.totalXP}</div>
            <p className="text-blue-100">Points XP</p>
          </div>
        </div>
      </div>

      {/* Statistiques principales - 3 cartes principales */}
      <div className="grid gap-6 md:grid-cols-3">
        <Card className="border-l-4 border-l-green-500">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-slate-600">
                  Quiz Complétés
                </p>
                <p className="text-2xl font-bold text-slate-900">
                  {stats.completedQuizzes}
                </p>
                <p className="text-xs text-slate-500">
                  sur {totalQuizzes} disponibles
                </p>
              </div>
              <div className="p-3 bg-green-100 rounded-full">
                <BookOpen className="h-6 w-6 text-green-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="border-l-4 border-l-purple-500">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-slate-600">
                  Score Moyen
                </p>
                <p className="text-2xl font-bold text-slate-900">
                  {stats.averageScore}%
                </p>
                <p className="text-xs text-slate-500">
                  {stats.passedQuizzes} quiz réussis
                </p>
              </div>
              <div className="p-3 bg-purple-100 rounded-full">
                <Target className="h-6 w-6 text-purple-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="border-l-4 border-l-orange-500">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-slate-600">Projets</p>
                <p className="text-2xl font-bold text-slate-900">
                  {projects.length}
                </p>
                <p className="text-xs text-slate-500">projets disponibles</p>
              </div>
              <div className="p-3 bg-orange-100 rounded-full">
                <Code2 className="h-6 w-6 text-orange-600" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Activité récente - Une seule section claire */}
      {recentQuizzes.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Clock className="h-5 w-5" />
              Activité Récente
            </CardTitle>
            <CardDescription>Vos derniers quiz complétés</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {recentQuizzes.map((quiz, index) => (
                <div
                  key={index}
                  className="flex items-center justify-between p-3 bg-slate-50 rounded-lg"
                >
                  <div className="flex items-center gap-3">
                    <div
                      className={`p-2 rounded-full ${
                        quiz.passed ? "bg-green-100" : "bg-red-100"
                      }`}
                    >
                      {quiz.passed ? (
                        <CheckCircle className="h-4 w-4 text-green-600" />
                      ) : (
                        <Clock className="h-4 w-4 text-red-600" />
                      )}
                    </div>
                    <div>
                      <p className="font-medium text-slate-900">
                        {quiz.quizTitle}
                      </p>
                      <p className="text-sm text-slate-500">
                        {new Date(quiz.completedAt).toLocaleDateString("fr-FR")}
                      </p>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className="text-lg font-bold text-slate-900">
                      {quiz.score}%
                    </p>
                    <Badge
                      variant={quiz.passed ? "default" : "destructive"}
                      className="text-xs"
                    >
                      {quiz.passed ? "Réussi" : "Échoué"}
                    </Badge>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Compétences CV - Section simplifiée */}
      {userData?.skills && userData.skills.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Vos Compétences</CardTitle>
            <CardDescription>
              Technologies extraites de votre CV ({userData.skills.length}{" "}
              compétences)
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex flex-wrap gap-2">
              {userData.skills.map((skill, index) => (
                <Badge
                  key={index}
                  variant="outline"
                  className="text-sm py-1 px-3"
                >
                  {skill}
                </Badge>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
