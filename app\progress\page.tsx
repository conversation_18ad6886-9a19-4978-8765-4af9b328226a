"use client";

import {
  Award,
  BookOpen,
  Calendar,
  Code2,
  TrendingUp,
  FileText,
  Target,
  CheckCircle,
} from "lucide-react";
import { Badge } from "@/components/ui/badge";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { useEffect, useState } from "react";

interface UserProgressData {
  user: {
    email: string;
    firstName: string;
    lastName: string;
    skills: string[];
    cvUrl?: string;
  };
  statistics: {
    completedQuizzes: number;
    passedQuizzes: number;
    averageScore: number;
    totalXP: number;
  };
  quizResults: Array<{
    quizId: string;
    quizTitle: string;
    score: number;
    passed: boolean;
    completedAt: string;
    difficulty: string;
  }>;
}

interface Project {
  _id: string;
  title: string;
  difficulty: string;
  technologies: string[];
  createdAt: string;
}

interface Quiz {
  _id: string;
  title: string;
  difficulty: string;
  category: string;
}

export default function ProgressPage() {
  const [userProgress, setUserProgress] = useState<UserProgressData | null>(
    null
  );
  const [projects, setProjects] = useState<Project[]>([]);
  const [allQuizzes, setAllQuizzes] = useState<Quiz[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchData = async () => {
      try {
        const userEmail = localStorage.getItem("userEmail");
        if (!userEmail) {
          setError("Aucun utilisateur connecté");
          setLoading(false);
          return;
        }

        // Récupérer les données utilisateur avec ses résultats de quiz
        const userResponse = await fetch("/api/user", {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({ email: userEmail }),
        });

        if (userResponse.ok) {
          const userData = await userResponse.json();
          setUserProgress(userData);
        } else {
          setError("Erreur lors du chargement des données utilisateur");
        }

        // Récupérer tous les projets
        const projectsResponse = await fetch("/api/projects");
        if (projectsResponse.ok) {
          const projectsData = await projectsResponse.json();
          setProjects(projectsData);
        }

        // Récupérer tous les quiz
        const quizzesResponse = await fetch("/api/quiz");
        if (quizzesResponse.ok) {
          const quizzesData = await quizzesResponse.json();
          setAllQuizzes(quizzesData);
        }
      } catch (error) {
        console.error("Erreur lors du chargement des données:", error);
        setError("Erreur de connexion");
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  if (loading) {
    return (
      <div className="text-center py-10">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
        <p>Chargement de vos progrès...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center py-10">
        <div className="text-red-500 mb-4">
          <FileText className="h-12 w-12 mx-auto mb-2 opacity-50" />
          <p>{error}</p>
        </div>
        <button
          onClick={() => window.location.reload()}
          className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
        >
          Réessayer
        </button>
      </div>
    );
  }

  // Extraire les données ou utiliser des valeurs par défaut
  const stats = userProgress?.statistics || {
    completedQuizzes: 0,
    passedQuizzes: 0,
    averageScore: 0,
    totalXP: 0,
  };

  const userData = userProgress?.user;
  const totalProjects = projects.length;
  const totalQuizzes = allQuizzes.length;
  const skillsCount = userData?.skills.length || 0;

  // Calculer la progression par technologie basée sur les compétences du CV
  const skillProgress =
    userData?.skills.map((skill) => {
      const relatedProjects = projects.filter((p) =>
        p.technologies.some((tech) =>
          tech.toLowerCase().includes(skill.toLowerCase())
        )
      ).length;
      const progress = Math.min(relatedProjects * 20 + 30, 100);

      return {
        name: skill,
        progress: Math.round(progress),
        level:
          progress > 70
            ? "Avancé"
            : progress > 40
            ? "Intermédiaire"
            : "Débutant",
        color: getSkillColor(skill),
      };
    }) || [];

  function getSkillColor(skill: string) {
    const colors = [
      "bg-blue-500",
      "bg-green-500",
      "bg-purple-500",
      "bg-yellow-500",
      "bg-red-500",
      "bg-indigo-500",
    ];
    return colors[skill.length % colors.length];
  }

  // Générer des réalisations basées sur les vraies données
  const achievements = [
    {
      title: "Premier Quiz",
      description: "Complété votre premier quiz",
      icon: "🎯",
      earned: stats.completedQuizzes > 0,
    },
    {
      title: "Explorateur",
      description: "Consulté des projets",
      icon: "🏗️",
      earned: totalProjects > 0,
    },
    {
      title: "Maître des Quiz",
      description: "Complété au moins 5 quiz",
      icon: "🧠",
      earned: stats.completedQuizzes >= 5,
    },
    {
      title: "Expert Technologique",
      description: "Maîtrise 5 technologies ou plus",
      icon: "⚔️",
      earned: skillsCount >= 5,
    },
    {
      title: "Perfectionniste",
      description: "Score moyen supérieur à 80%",
      icon: "🏆",
      earned: stats.averageScore >= 80,
    },
    {
      title: "Champion",
      description: "Réussi 10 quiz ou plus",
      icon: "👑",
      earned: stats.passedQuizzes >= 10,
    },
  ];

  // Activité récente basée sur les vraies données
  const recentActivity = [
    // Projets récents
    ...projects.slice(0, 2).map((project) => ({
      type: "project" as const,
      title: project.title,
      difficulty: project.difficulty,
      date: new Date(project.createdAt).toLocaleDateString("fr-FR"),
    })),
    // Quiz récents complétés
    ...(userProgress?.quizResults.slice(0, 3).map((result) => ({
      type: "quiz" as const,
      title: result.quizTitle,
      score: result.score,
      passed: result.passed,
      date: new Date(result.completedAt).toLocaleDateString("fr-FR"),
    })) || []),
  ];

  return (
    <div className="space-y-8">
      <div>
        <h1 className="text-3xl font-bold text-slate-900">Votre Progression</h1>
        <p className="text-slate-600 mt-2">
          Suivez votre parcours d'apprentissage et vos réalisations
        </p>
        {userData && (
          <p className="text-sm text-slate-500 mt-1">
            Connecté en tant que {userData.firstName} {userData.lastName}
          </p>
        )}
      </div>

      {/* Overview Stats */}
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Points XP</CardTitle>
            <TrendingUp className="h-4 w-4 text-blue-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.totalXP}</div>
            <p className="text-xs text-muted-foreground">Points d'expérience</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Quiz Complétés
            </CardTitle>
            <BookOpen className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.completedQuizzes}</div>
            <p className="text-xs text-muted-foreground">
              sur {totalQuizzes} disponibles
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Score Moyen</CardTitle>
            <Target className="h-4 w-4 text-purple-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.averageScore}%</div>
            <p className="text-xs text-muted-foreground">
              {stats.passedQuizzes} quiz réussis
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Compétences CV
            </CardTitle>
            <Award className="h-4 w-4 text-orange-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{skillsCount}</div>
            <p className="text-xs text-muted-foreground">
              Technologies extraites
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Skill Progress */}
      {skillProgress.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Progression par Technologie</CardTitle>
            <CardDescription>
              Votre progression dans les technologies extraites de votre CV
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            {skillProgress.map((skill) => (
              <div key={skill.name} className="space-y-2">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <div className={`w-3 h-3 rounded-full ${skill.color}`} />
                    <span className="font-medium">{skill.name}</span>
                    <Badge variant="secondary" className="text-xs">
                      {skill.level}
                    </Badge>
                  </div>
                  <span className="text-sm font-medium">{skill.progress}%</span>
                </div>
                <Progress value={skill.progress} className="h-2" />
              </div>
            ))}
          </CardContent>
        </Card>
      )}

      <div className="grid gap-6 lg:grid-cols-2">
        {/* Achievements */}
        <Card>
          <CardHeader>
            <CardTitle>Réalisations</CardTitle>
            <CardDescription>
              Badges que vous avez gagnés dans votre parcours
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid gap-4">
              {achievements.map((achievement, index) => (
                <div
                  key={index}
                  className={`flex items-center gap-4 p-3 rounded-lg border ${
                    achievement.earned
                      ? "bg-green-50 border-green-200"
                      : "bg-slate-50 border-slate-200 opacity-60"
                  }`}
                >
                  <div className="text-2xl">{achievement.icon}</div>
                  <div className="flex-1">
                    <h4 className="font-medium">{achievement.title}</h4>
                    <p className="text-sm text-slate-600">
                      {achievement.description}
                    </p>
                  </div>
                  {achievement.earned && (
                    <Badge className="bg-green-100 text-green-800 border-green-200">
                      Obtenu
                    </Badge>
                  )}
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Recent Activity */}
        <Card>
          <CardHeader>
            <CardTitle>Activité Récente</CardTitle>
            <CardDescription>
              Vos dernières activités d'apprentissage
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {recentActivity.length > 0 ? (
                recentActivity.map((activity, index) => (
                  <div
                    key={index}
                    className="flex items-center gap-4 p-3 rounded-lg bg-slate-50"
                  >
                    <div className="flex-shrink-0">
                      {activity.type === "quiz" ? (
                        <BookOpen className="h-5 w-5 text-blue-600" />
                      ) : (
                        <Code2 className="h-5 w-5 text-green-600" />
                      )}
                    </div>
                    <div className="flex-1">
                      <h4 className="font-medium">{activity.title}</h4>
                      <div className="flex items-center gap-2 text-sm text-slate-600">
                        {activity.type === "quiz" && (
                          <>
                            <span>Score: {activity.score}%</span>
                            {activity.passed && (
                              <Badge
                                variant="outline"
                                className="text-xs bg-green-50 text-green-700"
                              >
                                Réussi
                              </Badge>
                            )}
                          </>
                        )}
                        {activity.type === "project" && activity.difficulty && (
                          <Badge variant="outline" className="text-xs">
                            {activity.difficulty}
                          </Badge>
                        )}
                        <span>•</span>
                        <span>{activity.date}</span>
                      </div>
                    </div>
                  </div>
                ))
              ) : (
                <div className="text-center py-8 text-slate-500">
                  <FileText className="h-12 w-12 mx-auto mb-4 opacity-50" />
                  <p>Aucune activité récente</p>
                  <p className="text-sm">
                    Commencez par passer un quiz pour voir vos progrès !
                  </p>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* User Skills Section */}
      {userData?.skills && userData.skills.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Vos Compétences</CardTitle>
            <CardDescription>
              Technologies extraites de votre CV
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex flex-wrap gap-2">
              {userData.skills.map((skill, index) => (
                <Badge key={index} variant="outline" className="text-sm">
                  {skill}
                </Badge>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
