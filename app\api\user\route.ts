import { NextRequest, NextResponse } from "next/server";

import User from "../../../models/User";
import dbConnect from "../../../lib/mongo";

export async function POST(req: NextRequest) {
  try {
    const { email } = await req.json();

    if (!email) {
      return NextResponse.json({ error: "Email manquant." }, { status: 400 });
    }

    await dbConnect();
    const user = await User.findOne({ email })
      .populate("quizResults.quiz")
      .populate("userProjects.project");

    if (!user) {
      return NextResponse.json(
        { error: "Utilisateur non trouvé." },
        { status: 404 }
      );
    }

    // Calculer les statistiques
    const quizResults = user.quizResults || [];
    console.log(`🔍 API /user - Email: ${email}`);
    console.log(`📊 Quiz results bruts:`, quizResults.length);
    console.log(
      `📝 Détails quiz results:`,
      quizResults.map((r) => ({
        quizId: r.quiz,
        score: r.score,
        passed: r.passed,
        date: r.completedAt,
      }))
    );

    const completedQuizzes = quizResults.length;
    const passedQuizzes = quizResults.filter(
      (result: any) => result.passed
    ).length;

    console.log(`✅ Quiz complétés calculés: ${completedQuizzes}`);
    console.log(`🎯 Quiz réussis: ${passedQuizzes}`);

    // Calculer les statistiques des projets
    const userProjects = user.userProjects || [];
    const totalUserProjects = userProjects.length;
    const completedProjects = userProjects.filter(
      (up: any) => up.status === "completed"
    ).length;
    const inProgressProjects = userProjects.filter(
      (up: any) => up.status === "in_progress"
    ).length;
    const totalTimeSpent = userProjects.reduce(
      (acc: number, up: any) => acc + (up.timeSpent || 0),
      0
    );

    console.log(`📁 Projets utilisateur: ${totalUserProjects}`);
    console.log(`✅ Projets complétés: ${completedProjects}`);
    console.log(`🚧 Projets en cours: ${inProgressProjects}`);
    const averageScore =
      completedQuizzes > 0
        ? Math.round(
            quizResults.reduce(
              (acc: number, result: any) => acc + result.score,
              0
            ) / completedQuizzes
          )
        : 0;

    // Calculer les points XP
    const totalXP = quizResults.reduce((acc: number, result: any) => {
      let points = result.score; // Points de base = score
      if (result.passed) points += 20; // Bonus pour réussir
      return acc + points;
    }, 0);

    return NextResponse.json({
      user: {
        email: user.email,
        firstName: user.firstName || "Utilisateur",
        lastName: user.lastName || "SkillForge",
        role: user.role || "user",
        permissions: user.permissions || [],
        skills: user.skills || [],
        cvUrl: user.cvUrl || null,
      },
      statistics: {
        completedQuizzes,
        passedQuizzes,
        averageScore,
        totalXP,
        // Statistiques des projets
        totalUserProjects,
        completedProjects,
        inProgressProjects,
        totalTimeSpent,
      },
      quizResults: quizResults.map((result: any) => ({
        quizId: result.quiz?._id,
        quizTitle: result.quiz?.title || "Quiz supprimé",
        score: result.score,
        passed: result.passed,
        completedAt: result.completedAt,
        difficulty: result.quiz?.difficulty || "facile",
      })),
      userProjects: userProjects.map((up: any) => ({
        _id: up._id,
        project: {
          _id: up.project?._id,
          title: up.project?.title || "Projet supprimé",
          difficulty: up.project?.difficulty || "Beginner",
          technologies: up.project?.technologies || [],
        },
        status: up.status,
        startedAt: up.startedAt,
        completedAt: up.completedAt,
        progress: up.progress,
        timeSpent: up.timeSpent,
        lastActivity: up.lastActivity,
      })),
    });
  } catch (err) {
    console.error("Erreur dans /api/user :", err);
    return NextResponse.json({ error: "Erreur serveur" }, { status: 500 });
  }
}
