import { NextRequest } from "next/server";
import User from "../models/User";
import dbConnect from "./mongo";

// Interface pour l'utilisateur authentifié
export interface AuthenticatedUser {
  _id: string;
  email: string;
  firstName: string;
  lastName: string;
  role: "user" | "admin";
  permissions: string[];
}

// Vérifier l'authentification via email (temporaire - à remplacer par JWT)
export async function authenticateUser(req: NextRequest): Promise<AuthenticatedUser | null> {
  try {
    // Pour l'instant, on récupère l'email depuis le body ou les headers
    const body = await req.json().catch(() => ({}));
    const email = body.email || req.headers.get("x-user-email");

    if (!email) {
      return null;
    }

    await dbConnect();
    const user = await User.findOne({ email }).select("-password");

    if (!user) {
      return null;
    }

    return {
      _id: user._id.toString(),
      email: user.email,
      firstName: user.firstName || "Utilisateur",
      lastName: user.lastName || "",
      role: user.role || "user",
      permissions: user.permissions || [],
    };
  } catch (error) {
    console.error("Erreur authentification:", error);
    return null;
  }
}

// Vérifier si l'utilisateur a une permission spécifique
export function hasPermission(user: AuthenticatedUser, permission: string): boolean {
  // Les admins ont toutes les permissions
  if (user.role === "admin") {
    return true;
  }

  // Vérifier les permissions spécifiques
  return user.permissions.includes(permission);
}

// Vérifier si l'utilisateur est admin
export function isAdmin(user: AuthenticatedUser): boolean {
  return user.role === "admin";
}

// Middleware pour vérifier l'authentification
export async function requireAuth(req: NextRequest): Promise<AuthenticatedUser> {
  const user = await authenticateUser(req);
  
  if (!user) {
    throw new Error("Authentication required");
  }

  return user;
}

// Middleware pour vérifier les permissions admin
export async function requireAdmin(req: NextRequest): Promise<AuthenticatedUser> {
  const user = await requireAuth(req);
  
  if (!isAdmin(user)) {
    throw new Error("Admin access required");
  }

  return user;
}

// Middleware pour vérifier une permission spécifique
export async function requirePermission(req: NextRequest, permission: string): Promise<AuthenticatedUser> {
  const user = await requireAuth(req);
  
  if (!hasPermission(user, permission)) {
    throw new Error(`Permission '${permission}' required`);
  }

  return user;
}

// Fonction pour promouvoir un utilisateur en admin
export async function promoteToAdmin(email: string): Promise<boolean> {
  try {
    await dbConnect();
    
    const result = await User.updateOne(
      { email },
      { 
        role: "admin",
        permissions: [
          "create_projects",
          "edit_projects", 
          "delete_projects",
          "create_quiz",
          "edit_quiz",
          "delete_quiz",
          "manage_users",
          "view_analytics"
        ]
      }
    );

    return result.modifiedCount > 0;
  } catch (error) {
    console.error("Erreur promotion admin:", error);
    return false;
  }
}

// Permissions par défaut pour les nouveaux admins
export const ADMIN_PERMISSIONS = [
  "create_projects",
  "edit_projects", 
  "delete_projects",
  "create_quiz",
  "edit_quiz",
  "delete_quiz",
  "manage_users",
  "view_analytics"
];

// Permissions par défaut pour les utilisateurs normaux
export const USER_PERMISSIONS: string[] = [];
