// Script pour créer le premier admin
// Usage: node scripts/create-admin.js <email>

const mongoose = require('mongoose');

// Configuration MongoDB (ajustez selon votre setup)
const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/skillforge';

const userSchema = new mongoose.Schema({
  email: { type: String, required: true, unique: true },
  password: { type: String, required: true },
  role: { 
    type: String, 
    enum: ["user", "admin"], 
    default: "user" 
  },
  permissions: {
    type: [String],
    default: [],
  },
  firstName: { type: String },
  lastName: { type: String },
  // ... autres champs
});

const User = mongoose.models.User || mongoose.model('User', userSchema);

const ADMIN_PERMISSIONS = [
  "create_projects",
  "edit_projects", 
  "delete_projects",
  "create_quiz",
  "edit_quiz",
  "delete_quiz",
  "manage_users",
  "view_analytics"
];

async function createAdmin(email) {
  try {
    console.log('🔌 Connexion à MongoDB...');
    await mongoose.connect(MONGODB_URI);
    
    console.log(`🔍 Recherche de l'utilisateur: ${email}`);
    const user = await User.findOne({ email });
    
    if (!user) {
      console.error(`❌ Utilisateur avec l'email ${email} non trouvé`);
      console.log('💡 Assurez-vous que l\'utilisateur s\'est d\'abord inscrit sur la plateforme');
      process.exit(1);
    }
    
    if (user.role === 'admin') {
      console.log(`✅ ${email} est déjà admin`);
      process.exit(0);
    }
    
    console.log(`🚀 Promotion de ${email} en admin...`);
    await User.updateOne(
      { email },
      { 
        role: 'admin',
        permissions: ADMIN_PERMISSIONS
      }
    );
    
    console.log(`✅ ${email} a été promu admin avec succès !`);
    console.log(`🔑 Permissions accordées:`, ADMIN_PERMISSIONS);
    
  } catch (error) {
    console.error('❌ Erreur:', error.message);
  } finally {
    await mongoose.disconnect();
    console.log('🔌 Déconnexion de MongoDB');
  }
}

// Récupérer l'email depuis les arguments
const email = process.argv[2];

if (!email) {
  console.log('📧 Usage: node scripts/create-admin.js <email>');
  console.log('📧 Exemple: node scripts/create-admin.js <EMAIL>');
  process.exit(1);
}

createAdmin(email);
